#!/usr/bin/env python
"""
System test script for document ingestion and knowledge graph building.

This script:
1. Cleans databases
2. Ingests sample documents via API
3. Monitors the process
4. Verifies results in PostgreSQL and Neo4j
"""
import os

import time
import json
import logging
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
API_URL = "http://localhost:8000"
TEST_DATA_DIR = Path("tests/data")
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub",
    "sample_pdf.pdf",
    "sample_url.txt"
]

# Check if test data directory exists
if not TEST_DATA_DIR.exists():
    logger.warning(f"Test data directory {TEST_DATA_DIR} not found. Using current directory.")
    TEST_DATA_DIR = Path(".")

def clean_databases():
    """Clean PostgreSQL and Neo4j databases."""
    logger.info("Cleaning databases...")

    # Clean PostgreSQL
    try:
        os.system("docker-compose exec postgres psql -U longevity -d longevity -c \"DELETE FROM processing_tasks;\"")
        os.system("docker-compose exec postgres psql -U longevity -d longevity -c \"DELETE FROM kg_chunks;\"")
        os.system("docker-compose exec postgres psql -U longevity -d longevity -c \"DELETE FROM chunks;\"")
        os.system("docker-compose exec postgres psql -U longevity -d longevity -c \"DELETE FROM documents;\"")
        os.system("docker-compose exec postgres psql -U longevity -d longevity -c \"DELETE FROM entities;\"")
        logger.info("PostgreSQL database cleaned")
    except Exception as e:
        logger.error(f"Error cleaning PostgreSQL: {e}")

    # Clean Neo4j
    try:
        os.system("docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (n) DETACH DELETE n;\"")
        logger.info("Neo4j database cleaned")
    except Exception as e:
        logger.error(f"Error cleaning Neo4j: {e}")

def ingest_document(file_path: str, is_url: bool = False) -> Optional[str]:
    """
    Ingest a document via the API.

    Args:
        file_path: Path to the document or URL
        is_url: Whether the file_path is a URL

    Returns:
        Optional[str]: Document ID if successful, None otherwise
    """
    try:
        if is_url:
            # Read URL from file
            with open(TEST_DATA_DIR / file_path, 'r') as f:
                url = f.read().strip()

            # Call API to ingest URL
            logger.info(f"Ingesting URL: {url}")
            response = requests.post(
                f"{API_URL}/api/documents/ingest",
                json={
                    "url": url,
                    "metadata_str": json.dumps({
                        "knowledge_graph": True,
                        "batch_api": True
                    })
                }
            )
        else:
            # Call API to ingest file
            logger.info(f"Ingesting file: {file_path}")
            with open(TEST_DATA_DIR / file_path, 'rb') as f:
                response = requests.post(
                    f"{API_URL}/api/documents/ingest",
                    files={"file": (file_path, f)},
                    data={
                        "metadata_str": json.dumps({
                            "knowledge_graph": True,
                            "batch_api": True
                        })
                    }
                )

        if response.status_code == 200:
            result = response.json()
            document_id = result.get("document_id")
            logger.info(f"Document ingested successfully. ID: {document_id}")
            return document_id
        else:
            logger.error(f"Error ingesting document: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logger.error(f"Exception during ingestion: {e}")
        return None

def check_document_status(document_id: str) -> Dict[str, Any]:
    """
    Check the status of a document.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Document status
    """
    try:
        response = requests.get(f"{API_URL}/api/documents/{document_id}")
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Error checking document status: {response.status_code} - {response.text}")
            return {}
    except Exception as e:
        logger.error(f"Exception checking document status: {e}")
        return {}

def check_processing_tasks(document_id: str) -> List[Dict[str, Any]]:
    """
    Check processing tasks for a document.

    Args:
        document_id: Document ID

    Returns:
        List[Dict[str, Any]]: List of processing tasks
    """
    try:
        response = requests.get(f"{API_URL}/api/tasks?document_id={document_id}")
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Error checking processing tasks: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        logger.error(f"Exception checking processing tasks: {e}")
        return []

def check_chunks(document_id: str) -> Dict[str, int]:
    """
    Check chunks for a document.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, int]: Count of RAG and KG chunks
    """
    try:
        # Check RAG chunks
        rag_chunks_cmd = f"docker-compose exec postgres psql -U longevity -d longevity -c \"SELECT COUNT(*) FROM chunks WHERE document_id = '{document_id}';\""
        rag_chunks = os.popen(rag_chunks_cmd).read().strip().split("\n")[2].strip()

        # Check KG chunks
        kg_chunks_cmd = f"docker-compose exec postgres psql -U longevity -d longevity -c \"SELECT COUNT(*) FROM kg_chunks WHERE document_id = '{document_id}';\""
        kg_chunks = os.popen(kg_chunks_cmd).read().strip().split("\n")[2].strip()

        return {
            "rag_chunks": int(rag_chunks),
            "kg_chunks": int(kg_chunks)
        }
    except Exception as e:
        logger.error(f"Exception checking chunks: {e}")
        return {"rag_chunks": 0, "kg_chunks": 0}

def check_entities() -> int:
    """
    Check entities for a document.

    Args:
        document_id: Document ID

    Returns:
        int: Count of entities
    """
    try:
        # Check entities in PostgreSQL
        cmd = f"docker-compose exec postgres psql -U longevity -d longevity -c \"SELECT COUNT(*) FROM entities;\""
        output = os.popen(cmd).read().strip().split("\n")
        if len(output) >= 3:
            entities = output[2].strip()
            return int(entities)
        return 0
    except Exception as e:
        logger.error(f"Exception checking entities: {e}")
        return 0

def check_neo4j(document_id: str) -> Dict[str, int]:
    """
    Check Neo4j for a document.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, int]: Counts of entities, relationships, and chunks
    """
    try:
        # Check document in Neo4j
        doc_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (d:Document {{id: '{document_id}'}}) RETURN COUNT(d) AS count;\""
        output = os.popen(doc_cmd).read().strip().split("\n")
        doc_count = 0
        for line in output:
            if line.strip().isdigit():
                doc_count = int(line.strip())
                break

        # Check chunks in Neo4j
        chunks_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (c:Chunk)-[:PART_OF]->(d:Document {{id: '{document_id}'}}) RETURN COUNT(c) AS count;\""
        output = os.popen(chunks_cmd).read().strip().split("\n")
        chunks_count = 0
        for line in output:
            if line.strip().isdigit():
                chunks_count = int(line.strip())
                break

        # Check entities in Neo4j
        entities_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (e:Entity)-[:MENTIONED_IN]->(c:Chunk)-[:PART_OF]->(d:Document {{id: '{document_id}'}}) RETURN COUNT(DISTINCT e) AS count;\""
        output = os.popen(entities_cmd).read().strip().split("\n")
        entities_count = 0
        for line in output:
            if line.strip().isdigit():
                entities_count = int(line.strip())
                break

        # Check entity-entity relationships in Neo4j
        ee_rel_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (e1:Entity)-[r]->(e2:Entity) WHERE (e1)-[:MENTIONED_IN]->(:Chunk)-[:PART_OF]->(:Document {{id: '{document_id}'}}) RETURN COUNT(r) AS count;\""
        output = os.popen(ee_rel_cmd).read().strip().split("\n")
        ee_rel_count = 0
        for line in output:
            if line.strip().isdigit():
                ee_rel_count = int(line.strip())
                break

        # Check entity-chunk relationships in Neo4j
        ec_rel_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (e:Entity)-[r:MENTIONED_IN]->(c:Chunk)-[:PART_OF]->(d:Document {{id: '{document_id}'}}) RETURN COUNT(r) AS count;\""
        output = os.popen(ec_rel_cmd).read().strip().split("\n")
        ec_rel_count = 0
        for line in output:
            if line.strip().isdigit():
                ec_rel_count = int(line.strip())
                break

        # Check chunk-chunk relationships in Neo4j
        cc_rel_cmd = f"docker-compose exec neo4j cypher-shell -u neo4j -p password \"MATCH (c1:Chunk)-[r]->(c2:Chunk) WHERE (c1)-[:PART_OF]->(:Document {{id: '{document_id}'}}) RETURN COUNT(r) AS count;\""
        output = os.popen(cc_rel_cmd).read().strip().split("\n")
        cc_rel_count = 0
        for line in output:
            if line.strip().isdigit():
                cc_rel_count = int(line.strip())
                break

        return {
            "documents": doc_count,
            "chunks": chunks_count,
            "entities": entities_count,
            "entity_entity_relationships": ee_rel_count,
            "entity_chunk_relationships": ec_rel_count,
            "chunk_chunk_relationships": cc_rel_count
        }
    except Exception as e:
        logger.error(f"Exception checking Neo4j: {e}")
        return {
            "documents": 0,
            "chunks": 0,
            "entities": 0,
            "entity_entity_relationships": 0,
            "entity_chunk_relationships": 0,
            "chunk_chunk_relationships": 0
        }

def wait_for_completion(document_ids: List[str], timeout: int = 600) -> bool:
    """
    Wait for all documents to be processed.

    Args:
        document_ids: List of document IDs
        timeout: Timeout in seconds

    Returns:
        bool: True if all documents are processed, False otherwise
    """
    logger.info(f"Waiting for {len(document_ids)} documents to be processed...")
    start_time = time.time()

    while time.time() - start_time < timeout:
        all_completed = True

        for doc_id in document_ids:
            # Check processing tasks
            tasks = check_processing_tasks(doc_id)

            # Check if all tasks are completed
            for task in tasks:
                if task.get("status") not in ["completed", "failed"]:
                    all_completed = False
                    break

            if not all_completed:
                break

        if all_completed:
            logger.info("All documents processed!")
            return True

        logger.info("Still processing documents... waiting 10 seconds")
        time.sleep(10)

    logger.error(f"Timeout after {timeout} seconds")
    return False

def get_document_ids():
    """
    Get document IDs from the database.

    Returns:
        List[str]: List of document IDs
    """
    try:
        cmd = "docker-compose exec postgres psql -U longevity -d longevity -c \"SELECT id FROM documents ORDER BY created_at DESC LIMIT 10;\""
        output = os.popen(cmd).read().strip().split("\n")

        # Skip header lines
        document_ids = []
        for line in output[2:-1]:  # Skip header and footer lines
            doc_id = line.strip()
            if doc_id:
                document_ids.append(doc_id)

        logger.info(f"Found {len(document_ids)} documents in the database")
        return document_ids
    except Exception as e:
        logger.error(f"Exception getting document IDs: {e}")
        return []

def run_system_test():
    """Run the system test."""
    logger.info("Starting system test...")

    # Step 1: Clean databases
    clean_databases()

    # Step 2: Ingest documents
    for file in SAMPLE_FILES:
        is_url = file == "sample_url.txt"
        ingest_document(file, is_url)

    # Get document IDs from the database
    logger.info("Waiting 5 seconds for documents to be processed...")
    time.sleep(5)
    document_ids = get_document_ids()

    if not document_ids:
        logger.error("No documents were found in the database. Aborting test.")
        return

    # Step 3: Wait for processing to complete
    if not wait_for_completion(document_ids):
        logger.error("Document processing did not complete in time. Continuing with verification...")

    # Step 4: Verify results
    for doc_id in document_ids:
        logger.info(f"\n--- Verification for document {doc_id} ---")

        # Check chunks
        chunk_counts = check_chunks(doc_id)
        logger.info(f"RAG chunks: {chunk_counts['rag_chunks']}")
        logger.info(f"KG chunks: {chunk_counts['kg_chunks']}")

        # Check entities
        entity_count = check_entities(doc_id)
        logger.info(f"Entities in PostgreSQL: {entity_count}")

        # Check Neo4j
        neo4j_counts = check_neo4j(doc_id)
        logger.info(f"Neo4j document nodes: {neo4j_counts['documents']}")
        logger.info(f"Neo4j chunk nodes: {neo4j_counts['chunks']}")
        logger.info(f"Neo4j entity nodes: {neo4j_counts['entities']}")
        logger.info(f"Neo4j entity-entity relationships: {neo4j_counts['entity_entity_relationships']}")
        logger.info(f"Neo4j entity-chunk relationships: {neo4j_counts['entity_chunk_relationships']}")
        logger.info(f"Neo4j chunk-chunk relationships: {neo4j_counts['chunk_chunk_relationships']}")

        # Verify success criteria
        success = (
            chunk_counts['rag_chunks'] > 0 and
            chunk_counts['kg_chunks'] > 0 and
            entity_count > 0 and
            neo4j_counts['documents'] > 0 and
            neo4j_counts['chunks'] > 0 and
            neo4j_counts['entities'] > 0 and
            neo4j_counts['entity_chunk_relationships'] > 0
        )

        if success:
            logger.info(f"✅ Document {doc_id} processed successfully!")
        else:
            logger.error(f"❌ Document {doc_id} processing incomplete or failed!")

    logger.info("\nSystem test completed!")

if __name__ == "__main__":
    run_system_test()
