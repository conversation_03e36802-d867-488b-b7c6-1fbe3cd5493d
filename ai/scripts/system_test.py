#!/usr/bin/env python
"""
Comprehensive system test script for document ingestion and knowledge graph building.

This script follows the specified test rules:
1. <PERSON><PERSON> compose up, build
2. Remove all old data in databases
3. Start ingesting with full automation after calling ingest endpoint
4. Check RAG chunks and KG chunks are created
5. Verify entities extracted, normalized and inserted in PostgreSQL
6. Check Neo4j contains entities, relationships, chunks, and documents

Test files:
- PubMed articles with query "longevity diet" (2 articles)
- sample_txt.txt
- sample_epub.epub
- sample_pdf.pdf
- sample_url.txt (contains URL to fetch)
"""
import os
import sys
import time
import json
import logging
import requests
import subprocess
import psycopg2
from pathlib import Path
from typing import Dict, List, Any
from neo4j import GraphDatabase

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
API_URL = "http://localhost:8000"
TEST_DATA_DIR = Path("tests/data")
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub",
    "sample_pdf.pdf",
    "sample_url.txt"
]

# Test configuration
PUBMED_QUERY = "longevity diet"
PUBMED_MAX_RESULTS = 2
PROCESSING_TIMEOUT = 1800  # 30 minutes
CHECK_INTERVAL = 30  # 30 seconds


class SystemTestRunner:
    """Main system test runner class."""

    def __init__(self):
        """Initialize the system test runner."""
        self.api_url = API_URL
        self.test_data_dir = TEST_DATA_DIR
        self.ingestion_tasks = []

        # Check if test data directory exists
        if not self.test_data_dir.exists():
            logger.warning(f"Test data directory {self.test_data_dir} not found. Using current directory.")
            self.test_data_dir = Path(".")

    def run_docker_compose(self):
        """Step 1: Start Docker Compose services."""
        logger.info("=== STEP 1: Starting Docker Compose services ===")

        try:
            # Stop any existing containers
            logger.info("Stopping existing containers...")
            subprocess.run(["docker-compose", "down"], check=False, capture_output=True)

            # Build and start services
            logger.info("Building and starting services...")
            subprocess.run(
                ["docker-compose", "up", "--build", "-d"],
                check=True,
                capture_output=True,
                text=True
            )
            logger.info("Docker Compose services started successfully")

            # Wait for services to be ready
            logger.info("Waiting for services to be ready...")
            self._wait_for_services()

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start Docker Compose: {e}")
            return False

    def _wait_for_services(self):
        """Wait for all services to be ready."""
        max_attempts = 60  # 5 minutes
        attempt = 0

        while attempt < max_attempts:
            try:
                # Check API health
                response = requests.get(f"{self.api_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("API service is ready")
                    break
            except requests.exceptions.RequestException:
                pass

            attempt += 1
            logger.info(f"Waiting for services... (attempt {attempt}/{max_attempts})")
            time.sleep(5)

        if attempt >= max_attempts:
            raise Exception("Services did not become ready within timeout")

    def clean_databases(self):
        """Step 2: Clean all databases."""
        logger.info("=== STEP 2: Cleaning databases ===")

        try:
            # Run the purge script
            logger.info("Running database purge script...")
            subprocess.run(
                ["python", "purge_db_direct.py"],
                check=True,
                capture_output=True,
                text=True,
                cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            )
            logger.info("Databases cleaned successfully")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to clean databases: {e}")
            return False

    def ingest_pubmed_articles(self):
        """Step 3a: Ingest PubMed articles."""
        logger.info("=== STEP 3a: Ingesting PubMed articles ===")

        try:
            url = f"{self.api_url}/api/pubmed/insert_articles_with_query"
            payload = {
                "query": PUBMED_QUERY,
                "max_results": PUBMED_MAX_RESULTS
            }

            logger.info(f"Requesting PubMed articles with query: '{PUBMED_QUERY}', max_results: {PUBMED_MAX_RESULTS}")
            response = requests.post(url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            task_ids = result.get("task_ids", [])
            logger.info(f"PubMed ingestion started with {len(task_ids)} tasks: {task_ids}")

            self.ingestion_tasks.extend(task_ids)
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to ingest PubMed articles: {e}")
            return False

    def ingest_sample_files(self):
        """Step 3b: Ingest sample files."""
        logger.info("=== STEP 3b: Ingesting sample files ===")

        success_count = 0

        for filename in SAMPLE_FILES:
            file_path = self.test_data_dir / filename

            if not file_path.exists():
                logger.warning(f"Sample file not found: {file_path}")
                continue

            try:
                if filename == "sample_url.txt":
                    # Handle URL file specially
                    success = self._ingest_url_file(file_path)
                else:
                    # Handle regular file upload
                    success = self._ingest_file(file_path)

                if success:
                    success_count += 1

            except Exception as e:
                logger.error(f"Error ingesting {filename}: {e}")

        logger.info(f"Successfully started ingestion for {success_count}/{len(SAMPLE_FILES)} files")
        return success_count > 0

    def _ingest_file(self, file_path: Path):
        """Ingest a single file."""
        logger.info(f"Ingesting file: {file_path.name}")

        try:
            url = f"{self.api_url}/api/documents/ingest"

            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()

            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"File {file_path.name} ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for {file_path.name}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to ingest file {file_path.name}: {e}")
            return False

    def _ingest_url_file(self, file_path: Path):
        """Ingest URL from a text file."""
        logger.info(f"Ingesting URL from file: {file_path.name}")

        try:
            # Read URL from file
            with open(file_path, 'r') as f:
                url_to_ingest = f.read().strip()

            if not url_to_ingest:
                logger.error(f"No URL found in {file_path.name}")
                return False

            logger.info(f"Ingesting URL: {url_to_ingest}")

            # Send URL to ingest endpoint
            api_url = f"{self.api_url}/api/documents/ingest"
            data = {'url': url_to_ingest}

            response = requests.post(api_url, data=data, timeout=120)
            response.raise_for_status()

            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"URL ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for URL ingestion")
                return False

        except Exception as e:
            logger.error(f"Failed to ingest URL from {file_path.name}: {e}")
            return False

    def monitor_processing(self):
        """Step 4: Monitor processing until completion."""
        logger.info("=== STEP 4: Monitoring processing ===")

        if not self.ingestion_tasks:
            logger.warning("No ingestion tasks to monitor")
            return False

        logger.info(f"Monitoring {len(self.ingestion_tasks)} ingestion tasks")

        start_time = time.time()

        while time.time() - start_time < PROCESSING_TIMEOUT:
            # Check if all processing is complete
            if self._check_processing_complete():
                logger.info("All processing completed successfully!")
                return True

            logger.info("Processing still in progress...")
            time.sleep(CHECK_INTERVAL)

        logger.error(f"Processing timeout after {PROCESSING_TIMEOUT} seconds")
        return False

    def _check_processing_complete(self):
        """Check if all processing is complete by verifying data in databases."""
        try:
            # Check if we have documents, chunks, and entities
            postgres_ready = self._check_postgres_data()
            neo4j_ready = self._check_neo4j_data()

            return postgres_ready and neo4j_ready

        except Exception as e:
            logger.warning(f"Error checking processing completion: {e}")
            return False

    def _check_postgres_data(self):
        """Check if PostgreSQL has the expected data."""
        try:
            # Connect to PostgreSQL
            conn = psycopg2.connect(
                host="localhost",
                port="5435",
                database="longevity",
                user="longevity",
                password="longevitypass"
            )
            cursor = conn.cursor()

            # Check documents
            cursor.execute("SELECT COUNT(*) FROM documents")
            doc_count = cursor.fetchone()[0]

            # Check chunks
            cursor.execute("SELECT COUNT(*) FROM chunks")
            chunk_count = cursor.fetchone()[0]

            # Check KG chunks
            cursor.execute("SELECT COUNT(*) FROM kg_chunks")
            kg_chunk_count = cursor.fetchone()[0]

            # Check entities
            cursor.execute("SELECT COUNT(*) FROM entities")
            entity_count = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            logger.debug(f"PostgreSQL data: {doc_count} documents, {chunk_count} chunks, {kg_chunk_count} KG chunks, {entity_count} entities")

            # We expect at least some data in each table
            return doc_count > 0 and chunk_count > 0 and kg_chunk_count > 0 and entity_count > 0

        except Exception as e:
            logger.warning(f"Error checking PostgreSQL data: {e}")
            return False

    def _check_neo4j_data(self):
        """Check if Neo4j has the expected data."""
        try:
            # Connect to Neo4j
            driver = GraphDatabase.driver(
                "neo4j://localhost:7687",
                auth=("neo4j", "longevityneo4j")
            )

            with driver.session() as session:
                # Check documents
                result = session.run("MATCH (d:Document) RETURN count(d) as count")
                doc_count = result.single()["count"]

                # Check chunks
                result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
                chunk_count = result.single()["count"]

                # Check entities
                result = session.run("MATCH (e:Entity) RETURN count(e) as count")
                entity_count = result.single()["count"]

            driver.close()

            logger.debug(f"Neo4j data: {doc_count} documents, {chunk_count} chunks, {entity_count} entities")

            # We expect at least some data in each category
            return doc_count > 0 and chunk_count > 0 and entity_count > 0

        except Exception as e:
            logger.warning(f"Error checking Neo4j data: {e}")
            return False

    def verify_final_results(self):
        """Step 5: Final verification of all data."""
        logger.info("=== STEP 5: Final verification ===")

        # Detailed PostgreSQL verification
        postgres_success = self._verify_postgres_detailed()

        # Detailed Neo4j verification
        neo4j_success = self._verify_neo4j_detailed()

        if postgres_success and neo4j_success:
            logger.info("✅ All verification checks passed!")
            return True
        else:
            logger.error("❌ Some verification checks failed!")
            return False

    def _verify_postgres_detailed(self):
        """Detailed PostgreSQL verification."""
        logger.info("Verifying PostgreSQL data in detail...")

        try:
            conn = psycopg2.connect(
                host="localhost",
                port="5435",
                database="longevity",
                user="longevity",
                password="longevitypass"
            )
            cursor = conn.cursor()

            # Check documents table
            cursor.execute("SELECT COUNT(*) FROM documents")
            doc_count = cursor.fetchone()[0]
            logger.info(f"✓ Documents: {doc_count}")

            # Check chunks table (RAG chunks)
            cursor.execute("SELECT COUNT(*) FROM chunks")
            chunk_count = cursor.fetchone()[0]
            logger.info(f"✓ RAG Chunks: {chunk_count}")

            # Check kg_chunks table
            cursor.execute("SELECT COUNT(*) FROM kg_chunks")
            kg_chunk_count = cursor.fetchone()[0]
            logger.info(f"✓ KG Chunks: {kg_chunk_count}")

            # Check entities table
            cursor.execute("SELECT COUNT(*) FROM entities")
            entity_count = cursor.fetchone()[0]
            logger.info(f"✓ Entities: {entity_count}")

            # Check relationships table
            cursor.execute("SELECT COUNT(*) FROM relationships")
            relationship_count = cursor.fetchone()[0]
            logger.info(f"✓ Relationships: {relationship_count}")

            # Check entity types
            cursor.execute("SELECT entity_type, COUNT(*) FROM entities GROUP BY entity_type")
            entity_types = cursor.fetchall()
            logger.info(f"✓ Entity types: {dict(entity_types)}")

            cursor.close()
            conn.close()

            # Verify minimum expected counts
            success = (
                doc_count >= 2 and  # At least 2 PubMed + some files
                chunk_count > 0 and
                kg_chunk_count > 0 and
                entity_count > 0 and
                relationship_count > 0
            )

            if success:
                logger.info("✅ PostgreSQL verification passed")
            else:
                logger.error("❌ PostgreSQL verification failed - insufficient data")

            return success

        except Exception as e:
            logger.error(f"❌ PostgreSQL verification error: {e}")
            return False

    def _verify_neo4j_detailed(self):
        """Detailed Neo4j verification."""
        logger.info("Verifying Neo4j data in detail...")

        try:
            driver = GraphDatabase.driver(
                "neo4j://localhost:7687",
                auth=("neo4j", "longevityneo4j")
            )

            with driver.session() as session:
                # Check documents
                result = session.run("MATCH (d:Document) RETURN count(d) as count")
                doc_count = result.single()["count"]
                logger.info(f"✓ Document nodes: {doc_count}")

                # Check chunks
                result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
                chunk_count = result.single()["count"]
                logger.info(f"✓ Chunk nodes: {chunk_count}")

                # Check entities
                result = session.run("MATCH (e:Entity) RETURN count(e) as count")
                entity_count = result.single()["count"]
                logger.info(f"✓ Entity nodes: {entity_count}")

                # Check entity-entity relationships
                result = session.run("MATCH (e1:Entity)-[r]-(e2:Entity) RETURN count(r) as count")
                entity_rel_count = result.single()["count"]
                logger.info(f"✓ Entity-Entity relationships: {entity_rel_count}")

                # Check entity-chunk relationships
                result = session.run("MATCH (e:Entity)-[r]-(c:Chunk) RETURN count(r) as count")
                entity_chunk_rel_count = result.single()["count"]
                logger.info(f"✓ Entity-Chunk relationships: {entity_chunk_rel_count}")

                # Check document-chunk relationships
                result = session.run("MATCH (d:Document)-[r]-(c:Chunk) RETURN count(r) as count")
                doc_chunk_rel_count = result.single()["count"]
                logger.info(f"✓ Document-Chunk relationships: {doc_chunk_rel_count}")

            driver.close()

            # Verify minimum expected counts
            success = (
                doc_count > 0 and
                chunk_count > 0 and
                entity_count > 0 and
                entity_chunk_rel_count > 0 and
                doc_chunk_rel_count > 0
            )

            if success:
                logger.info("✅ Neo4j verification passed")
            else:
                logger.error("❌ Neo4j verification failed - insufficient data")

            return success

        except Exception as e:
            logger.error(f"❌ Neo4j verification error: {e}")
            return False


def main():
    """Main function to run the system test."""
    import argparse

    parser = argparse.ArgumentParser(description="Comprehensive System Test for Document Ingestion")
    parser.add_argument("--skip-docker", action="store_true", help="Skip Docker Compose startup")
    parser.add_argument("--skip-cleanup", action="store_true", help="Skip database cleanup")
    parser.add_argument("--timeout", type=int, default=1800, help="Processing timeout in seconds")

    args = parser.parse_args()

    # Create test runner
    test_runner = SystemTestRunner()

    # Override timeout if specified
    global PROCESSING_TIMEOUT
    PROCESSING_TIMEOUT = args.timeout

    try:
        logger.info("🚀 Starting Comprehensive System Test")
        logger.info("=" * 60)

        # Step 1: Docker Compose (optional)
        if not args.skip_docker:
            if not test_runner.run_docker_compose():
                logger.error("❌ Failed to start Docker Compose")
                return 1
        else:
            logger.info("⏭️  Skipping Docker Compose startup")

        # Step 2: Database cleanup (optional)
        if not args.skip_cleanup:
            if not test_runner.clean_databases():
                logger.error("❌ Failed to clean databases")
                return 1
        else:
            logger.info("⏭️  Skipping database cleanup")

        # Step 3: Run ingestion
        logger.info("📥 Starting document ingestion...")

        # Ingest PubMed articles
        if not test_runner.ingest_pubmed_articles():
            logger.warning("⚠️  PubMed ingestion failed, continuing with files...")

        # Ingest sample files
        if not test_runner.ingest_sample_files():
            logger.error("❌ Failed to ingest sample files")
            return 1

        # Step 4: Monitor processing
        logger.info("⏳ Monitoring processing...")
        if not test_runner.monitor_processing():
            logger.warning("⚠️  Processing monitoring timed out, proceeding to verification...")

        # Step 5: Final verification
        logger.info("🔍 Running final verification...")
        if test_runner.verify_final_results():
            logger.info("🎉 SYSTEM TEST PASSED! All verification checks successful.")
            return 0
        else:
            logger.error("❌ SYSTEM TEST FAILED! Some verification checks failed.")
            return 1

    except KeyboardInterrupt:
        logger.info("⏹️  Test interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"❌ System test failed with unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)