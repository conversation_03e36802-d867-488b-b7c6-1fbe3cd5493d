#!/bin/bash
set -e

echo "PostgreSQL custom initialization starting"

# Create a temporary directory for processed scripts
mkdir -p /tmp/postgres-init

# Process initialization scripts without modifying originals
echo "Processing SQL files..."
for f in /docker-entrypoint-initdb.d/*.sql; do
  filename=$(basename "$f")
  echo "Processing $filename"
  # Use cat and redirect instead of sed -i to avoid in-place modification
  cat "$f" | sed "s/:POSTGRES_DB/$POSTGRES_DB/g" > "/tmp/postgres-init/$filename"
  chmod 755 "/tmp/postgres-init/$filename"
done

# Execute the original entrypoint with custom init scripts
echo "Starting PostgreSQL with custom init..."
DOCKER_ENTRYPOINT_INITDB_ARGS="--initdb-dir=/tmp/postgres-init" exec docker-entrypoint.sh "$@"